import { Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { ThemeProvider } from '@/components/theme-provider'
import { AuthGuard } from '@/components/auth-guard'
import { Layout } from '@/components/layout/layout'

// 页面组件
import { LoginPage } from '@/pages/auth/login'
import { DashboardPage } from '@/pages/dashboard'
import { QuestionsPage } from '@/pages/questions'
import { KnowledgePointsPage } from '@/pages/knowledge-points'
import { AnnotationPage } from '@/pages/annotation'
import { UsersPage } from '@/pages/admin/users'
import { SettingsPage } from '@/pages/settings'
import { NotFoundPage } from '@/pages/not-found'

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="annotation-ui-theme">
      <div className="min-h-screen bg-background">
        <Routes>
          {/* 公开路由 */}
          <Route path="/login" element={<LoginPage />} />

          {/* 受保护的路由 */}
          <Route
            path="/"
            element={
              <AuthGuard>
                <Layout />
              </AuthGuard>
            }
          >
            {/* 重定向根路径到仪表板 */}
            <Route index element={<Navigate to="/dashboard" replace />} />

            {/* 主要功能页面 */}
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="questions" element={<QuestionsPage />} />
            <Route path="knowledge-points" element={<KnowledgePointsPage />} />
            <Route path="annotation" element={<AnnotationPage />} />
            <Route path="settings" element={<SettingsPage />} />

            {/* 管理员专用路由 */}
            <Route path="admin/users" element={<UsersPage />} />
          </Route>

          {/* 404 页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* 全局通知组件 */}
        <Toaster />
      </div>
    </ThemeProvider>
  )
}

export default App
