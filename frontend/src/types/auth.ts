export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface User {
  user_id: number
  username: string
  email: string
  full_name: string
  role: UserRole
  is_active: boolean
  last_login?: string
  login_count: number
  created_at: string
  updated_at: string
}

export type UserRole = 'admin' | 'annotator' | 'reviewer' | 'viewer' | 'engine'

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  fullName: string
  role?: UserRole
}

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface RefreshTokenResponse {
  accessToken: string
  tokenType: string
}

export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

// 权限相关
export interface Permission {
  resource: string
  action: string
}

export const PERMISSIONS = {
  // 题目权限
  QUESTIONS_READ: 'questions:read',
  QUESTIONS_WRITE: 'questions:write',
  QUESTIONS_DELETE: 'questions:delete',
  
  // 知识点权限
  KNOWLEDGE_POINTS_READ: 'knowledge_points:read',
  KNOWLEDGE_POINTS_WRITE: 'knowledge_points:write',
  KNOWLEDGE_POINTS_DELETE: 'knowledge_points:delete',
  
  // 标注权限
  ANNOTATION_READ: 'annotation:read',
  ANNOTATION_WRITE: 'annotation:write',
  ANNOTATION_REVIEW: 'annotation:review',
  
  // 用户管理权限
  USERS_READ: 'users:read',
  USERS_WRITE: 'users:write',
  USERS_DELETE: 'users:delete',
  
  // 系统管理权限
  SYSTEM_ADMIN: 'system:admin',
} as const

export type PermissionKey = typeof PERMISSIONS[keyof typeof PERMISSIONS]

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, PermissionKey[]> = {
  admin: [
    PERMISSIONS.QUESTIONS_READ,
    PERMISSIONS.QUESTIONS_WRITE,
    PERMISSIONS.QUESTIONS_DELETE,
    PERMISSIONS.KNOWLEDGE_POINTS_READ,
    PERMISSIONS.KNOWLEDGE_POINTS_WRITE,
    PERMISSIONS.KNOWLEDGE_POINTS_DELETE,
    PERMISSIONS.ANNOTATION_READ,
    PERMISSIONS.ANNOTATION_WRITE,
    PERMISSIONS.ANNOTATION_REVIEW,
    PERMISSIONS.USERS_READ,
    PERMISSIONS.USERS_WRITE,
    PERMISSIONS.USERS_DELETE,
    PERMISSIONS.SYSTEM_ADMIN,
  ],
  annotator: [
    PERMISSIONS.QUESTIONS_READ,
    PERMISSIONS.KNOWLEDGE_POINTS_READ,
    PERMISSIONS.ANNOTATION_READ,
    PERMISSIONS.ANNOTATION_WRITE,
  ],
  reviewer: [
    PERMISSIONS.QUESTIONS_READ,
    PERMISSIONS.KNOWLEDGE_POINTS_READ,
    PERMISSIONS.ANNOTATION_READ,
    PERMISSIONS.ANNOTATION_REVIEW,
  ],
  viewer: [
    PERMISSIONS.QUESTIONS_READ,
    PERMISSIONS.KNOWLEDGE_POINTS_READ,
    PERMISSIONS.ANNOTATION_READ,
  ],
  engine: [
    PERMISSIONS.QUESTIONS_READ,
    PERMISSIONS.KNOWLEDGE_POINTS_READ,
  ],
}
