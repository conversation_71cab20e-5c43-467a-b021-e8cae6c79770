"""
题目管理服务类
"""

from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, text

from app.models.question import Question, QuestionAsset, ItemParam
from app.models.mapping import ItemKpMap
from app.models.knowledge import KnowledgePoint
from app.schemas.question import QuestionCreate, QuestionUpdate, QuestionImportItem


class QuestionService:
    """题目服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get(self, question_id: int) -> Optional[Question]:
        """根据ID获取题目"""
        return self.db.query(Question).filter(
            Question.question_id == question_id,
            Question.is_active == True
        ).first()
    
    def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Question]:
        """获取题目列表"""
        query = self.db.query(Question).filter(Question.is_active == True)
        
        # 搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Question.content['stem'].astext.ilike(search_term),
                    Question.analysis.ilike(search_term),
                    Question.source.ilike(search_term)
                )
            )
        
        # 过滤条件
        if filters:
            if 'subject' in filters:
                # 假设subject存储在tags中
                query = query.filter(Question.tags.contains([filters['subject']]))
            
            if 'difficulty' in filters:
                query = query.filter(Question.difficulty_lvl == int(filters['difficulty']))
            
            if 'q_type' in filters:
                query = query.filter(Question.q_type == int(filters['q_type']))
            
            if 'irt_ready' in filters:
                query = query.filter(Question.irt_ready == bool(filters['irt_ready']))
        
        return query.offset(skip).limit(limit).all()
    
    def count(
        self, 
        search: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计题目数量"""
        query = self.db.query(func.count(Question.question_id)).filter(Question.is_active == True)
        
        # 搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Question.content['stem'].astext.ilike(search_term),
                    Question.analysis.ilike(search_term),
                    Question.source.ilike(search_term)
                )
            )
        
        # 过滤条件
        if filters:
            if 'subject' in filters:
                query = query.filter(Question.tags.contains([filters['subject']]))
            
            if 'difficulty' in filters:
                query = query.filter(Question.difficulty_lvl == int(filters['difficulty']))
            
            if 'q_type' in filters:
                query = query.filter(Question.q_type == int(filters['q_type']))
            
            if 'irt_ready' in filters:
                query = query.filter(Question.irt_ready == bool(filters['irt_ready']))
        
        return query.scalar()
    
    def create(self, question_in: QuestionCreate, creator_id: int) -> Question:
        """创建题目"""
        question_data = question_in.model_dump()
        question_data['created_by'] = creator_id
        question_data['updated_by'] = creator_id
        
        question = Question(**question_data)
        self.db.add(question)
        self.db.commit()
        self.db.refresh(question)
        return question
    
    def update(self, question: Question, question_in: QuestionUpdate) -> Question:
        """更新题目"""
        update_data = question_in.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(question, field, value)
        
        self.db.commit()
        self.db.refresh(question)
        return question
    
    def remove(self, question_id: int) -> bool:
        """删除题目（软删除）"""
        question = self.get(question_id)
        if question:
            question.is_active = False
            self.db.commit()
            return True
        return False
    
    def get_knowledge_points(self, question_id: int) -> List[Dict[str, Any]]:
        """获取题目关联的知识点"""
        mappings = self.db.query(ItemKpMap).options(
            joinedload(ItemKpMap.knowledge_point)
        ).filter(ItemKpMap.question_id == question_id).all()
        
        result = []
        for mapping in mappings:
            result.append({
                'kp_id': mapping.kp_id,
                'name': mapping.knowledge_point.name,
                'code': mapping.knowledge_point.code,
                'is_required': mapping.is_required,
                'weight': mapping.weight,
                'confidence': mapping.confidence,
                'source': mapping.source
            })
        
        return result
    
    def get_subjects(self) -> List[str]:
        """获取所有学科列表"""
        # 从tags中提取学科信息
        result = self.db.execute(
            text("SELECT DISTINCT unnest(tags) as subject FROM questions WHERE is_active = true")
        ).fetchall()
        
        subjects = [row[0] for row in result if row[0]]
        return sorted(subjects)
    
    def get_difficulties(self) -> List[int]:
        """获取所有难度级别"""
        result = self.db.query(Question.difficulty_lvl).filter(
            Question.is_active == True,
            Question.difficulty_lvl.isnot(None)
        ).distinct().all()
        
        difficulties = [row[0] for row in result]
        return sorted(difficulties)
    
    def import_questions(self, questions: List[QuestionImportItem], creator_id: int) -> Dict[str, Any]:
        """批量导入题目"""
        success_count = 0
        error_count = 0
        errors = []
        created_questions = []
        
        for i, question_data in enumerate(questions):
            try:
                question = self.create(
                    QuestionCreate(**question_data.model_dump()), 
                    creator_id
                )
                created_questions.append(question.question_id)
                success_count += 1
            except Exception as e:
                error_count += 1
                errors.append(f"第{i+1}题导入失败: {str(e)}")
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'errors': errors,
            'created_questions': created_questions
        }
    
    def get_question_types(self) -> List[Dict[str, Any]]:
        """获取题目类型列表"""
        return [
            {'value': 0, 'label': '单选题'},
            {'value': 1, 'label': '多选题'},
            {'value': 2, 'label': '判断题'},
            {'value': 3, 'label': '填空题'},
            {'value': 4, 'label': '简答题'},
            {'value': 5, 'label': '论述题'},
            {'value': 6, 'label': '匹配题'},
            {'value': 7, 'label': '排序题'},
            {'value': 8, 'label': '计算题'},
            {'value': 9, 'label': '编程题'},
            {'value': 10, 'label': '复合题'}
        ]
    
    def validate_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证题目数据"""
        errors = []
        warnings = []
        
        # 检查必填字段
        if not question_data.get('content', {}).get('stem'):
            errors.append("题干不能为空")
        
        # 检查题目类型
        q_type = question_data.get('q_type')
        if q_type is None or q_type < 0 or q_type > 10:
            errors.append("题目类型无效")
        
        # 检查选择题是否有选项
        if q_type in [0, 1] and not question_data.get('content', {}).get('options'):
            errors.append("选择题必须有选项")
        
        # 检查难度等级
        difficulty = question_data.get('difficulty_lvl')
        if difficulty is not None and (difficulty < 1 or difficulty > 5):
            errors.append("难度等级必须在1-5之间")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
